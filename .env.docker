# TecnoDrive Docker Environment Configuration

# Database Configuration
MONGODB_URL=***********************************************************************
MONGODB_USERNAME=admin
MONGODB_PASSWORD=tecnodrive123
MONGODB_DATABASE=tecnodrive

# Redis Configuration
REDIS_URL=redis://redis:6379
REDIS_HOST=redis
REDIS_PORT=6379

# JWT Configuration
JWT_SECRET=tecnodrive-jwt-secret-key-2024-docker
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Service Discovery
EUREKA_URL=http://eureka-server:8761
EUREKA_HOST=eureka-server
EUREKA_PORT=8761

# API Gateway
GATEWAY_URL=http://api-gateway:8080
GATEWAY_HOST=api-gateway
GATEWAY_PORT=8080

# Service Ports
EUREKA_PORT=8761
GATEWAY_PORT=8080
AUTH_PORT=8081
USER_PORT=8083
FLEET_PORT=8084
LOCATION_PORT=8085
PAYMENT_PORT=8086
PARCEL_PORT=8087
NOTIFICATION_PORT=8088
ANALYTICS_PORT=8089
HR_PORT=8097
FINANCIAL_PORT=8098
WALLET_PORT=8099
LIVE_OPS_PORT=8100
OPERATIONS_PORT=8101
TRACKING_PORT=8102
DEMAND_PORT=8103

# Application Configuration
NODE_ENV=production
LOG_LEVEL=info
API_VERSION=v1

# Security Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:8080
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password

# File Upload Configuration
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,application/pdf

# External APIs
MAPS_API_KEY=your-maps-api-key
PAYMENT_GATEWAY_URL=https://api.payment-provider.com
PAYMENT_GATEWAY_KEY=your-payment-key

# Monitoring
HEALTH_CHECK_INTERVAL=30
METRICS_ENABLED=true

# Docker Network
DOCKER_NETWORK=tecnodrive-network
